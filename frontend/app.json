{"expo": {"name": "DonerLink", "slug": "DonerLink", "version": "1.0.0", "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "experiments": {"tsconfigPaths": true}, "plugins": [], "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "infoPlist": {"NSPhotoLibraryAddUsageDescription": "This app needs access to photo library to save blood donation reports."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["WRITE_EXTERNAL_STORAGE", "READ_EXTERNAL_STORAGE"]}}}