// Simple test to verify download functionality
// This can be run in the browser console or as a standalone test

const testDownloadFunctionality = () => {
  console.log('Testing download functionality...');
  
  // Test if required modules are available
  try {
    const FileSystem = require('expo-file-system');
    const MediaLibrary = require('expo-media-library');
    const Print = require('expo-print');
    
    console.log('✅ All required modules are available');
    console.log('FileSystem:', !!FileSystem);
    console.log('MediaLibrary:', !!MediaLibrary);
    console.log('Print:', !!Print);
    
    return true;
  } catch (error) {
    console.error('❌ Module import error:', error);
    return false;
  }
};

// Test permissions
const testPermissions = async () => {
  try {
    const { MediaLibrary } = require('expo-media-library');
    const { status } = await MediaLibrary.requestPermissionsAsync();
    console.log('Media Library Permission Status:', status);
    return status === 'granted';
  } catch (error) {
    console.error('Permission test error:', error);
    return false;
  }
};

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testDownloadFunctionality,
    testPermissions
  };
}

console.log('Download test utilities loaded');
