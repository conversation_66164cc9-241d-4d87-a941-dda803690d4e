// Test script to verify the fixed download functionality
// Run this in the app console or as a standalone test

const testDirectDownload = async () => {
  console.log('🧪 Testing Direct Download Functionality...');
  
  try {
    // Test FileSystem availability
    const FileSystem = require('expo-file-system');
    console.log('✅ FileSystem available:', !!FileSystem);
    
    // Test document directory access
    const docDir = FileSystem.documentDirectory;
    console.log('✅ Document directory:', docDir);
    
    // Test Downloads directory creation
    const downloadDir = docDir + 'Downloads/';
    const dirInfo = await FileSystem.getInfoAsync(downloadDir);
    console.log('✅ Downloads directory exists:', dirInfo.exists);
    
    if (!dirInfo.exists) {
      await FileSystem.makeDirectoryAsync(downloadDir, { intermediates: true });
      console.log('✅ Downloads directory created');
    }
    
    // Test file creation
    const testFile = downloadDir + 'test-download.txt';
    await FileSystem.writeAsStringAsync(testFile, 'Test download content');
    console.log('✅ Test file created:', testFile);
    
    // Verify file exists
    const fileInfo = await FileSystem.getInfoAsync(testFile);
    console.log('✅ Test file verified:', fileInfo.exists);
    
    // Clean up test file
    await FileSystem.deleteAsync(testFile);
    console.log('✅ Test file cleaned up');
    
    console.log('🎉 All download functionality tests passed!');
    return true;
    
  } catch (error) {
    console.error('❌ Download test failed:', error);
    return false;
  }
};

// Test MediaLibrary (optional)
const testMediaLibrary = async () => {
  try {
    const MediaLibrary = require('expo-media-library');
    const { status } = await MediaLibrary.getPermissionsAsync();
    console.log('📱 MediaLibrary permission status:', status);
    
    if (status === 'granted') {
      console.log('✅ MediaLibrary permissions granted');
    } else {
      console.log('⚠️ MediaLibrary permissions not granted (this is OK)');
    }
    
    return true;
  } catch (error) {
    console.error('❌ MediaLibrary test failed:', error);
    return false;
  }
};

// Export test functions
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testDirectDownload,
    testMediaLibrary
  };
}

// Auto-run tests if in development
if (__DEV__) {
  console.log('🔧 Development mode - download tests available');
  console.log('Run testDirectDownload() to test download functionality');
}
