// Test script to verify accessible download functionality
// This tests the new implementation that saves files to user-accessible locations

const testAccessibleDownload = async () => {
  console.log('🧪 Testing Accessible Download Functionality...');
  
  try {
    // Test required modules
    const FileSystem = require('expo-file-system');
    const MediaLibrary = require('expo-media-library');
    const Sharing = require('expo-sharing');
    
    console.log('✅ All modules available');
    console.log('FileSystem:', !!FileSystem);
    console.log('MediaLibrary:', !!MediaLibrary);
    console.log('Sharing:', !!Sharing);
    
    // Test MediaLibrary permissions
    const { status } = await MediaLibrary.getPermissionsAsync();
    console.log('📱 MediaLibrary permission status:', status);
    
    if (status === 'granted') {
      console.log('✅ MediaLibrary permissions granted - can save to device storage');
    } else {
      console.log('⚠️ MediaLibrary permissions not granted - will use sharing method');
    }
    
    // Test Sharing availability
    const sharingAvailable = await Sharing.isAvailableAsync();
    console.log('📤 Sharing available:', sharingAvailable);
    
    if (sharingAvailable) {
      console.log('✅ Sharing available - can use as download method');
    } else {
      console.log('⚠️ Sharing not available - will use app directory');
    }
    
    // Test file creation capability
    const testContent = 'Test PDF content for download verification';
    const testFile = FileSystem.documentDirectory + 'test-download.pdf';
    
    await FileSystem.writeAsStringAsync(testFile, testContent);
    const fileInfo = await FileSystem.getInfoAsync(testFile);
    console.log('✅ Test file created:', fileInfo.exists);
    
    // Test MediaLibrary save (if permissions granted)
    if (status === 'granted') {
      try {
        const asset = await MediaLibrary.createAssetAsync(testFile);
        console.log('✅ MediaLibrary save test successful');
        
        // Clean up test asset (optional)
        try {
          await MediaLibrary.deleteAssetsAsync([asset]);
          console.log('✅ Test asset cleaned up');
        } catch (cleanupError) {
          console.log('⚠️ Could not clean up test asset:', cleanupError);
        }
      } catch (mediaError) {
        console.log('❌ MediaLibrary save test failed:', mediaError);
      }
    }
    
    // Clean up test file
    await FileSystem.deleteAsync(testFile);
    console.log('✅ Test file cleaned up');
    
    // Summary
    console.log('\n📋 Download Method Summary:');
    if (status === 'granted') {
      console.log('🎯 Primary: MediaLibrary (saves to device storage)');
    } else if (sharingAvailable) {
      console.log('🎯 Primary: Sharing (user chooses save location)');
    } else {
      console.log('🎯 Primary: App directory (internal storage)');
    }
    
    console.log('🎉 Accessible download test completed!');
    return true;
    
  } catch (error) {
    console.error('❌ Accessible download test failed:', error);
    return false;
  }
};

// Test download flow simulation
const simulateDownloadFlow = async () => {
  console.log('🎬 Simulating Download Flow...');
  
  try {
    const MediaLibrary = require('expo-media-library');
    const Sharing = require('expo-sharing');
    
    // Check permissions
    const { status } = await MediaLibrary.getPermissionsAsync();
    const sharingAvailable = await Sharing.isAvailableAsync();
    
    console.log('📱 Current setup:');
    console.log('- MediaLibrary permission:', status);
    console.log('- Sharing available:', sharingAvailable);
    
    if (status === 'granted') {
      console.log('✅ Flow: PDF → MediaLibrary → Device Storage → Success');
    } else if (sharingAvailable) {
      console.log('✅ Flow: PDF → Sharing Dialog → User Saves → Success');
    } else {
      console.log('⚠️ Flow: PDF → App Directory → Limited Access');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Flow simulation failed:', error);
    return false;
  }
};

// Export test functions
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testAccessibleDownload,
    simulateDownloadFlow
  };
}

// Auto-run in development
if (__DEV__) {
  console.log('🔧 Accessible download tests loaded');
  console.log('Run testAccessibleDownload() to test functionality');
}
